from typing import List

from sqlalchemy.orm import Session
from sqlalchemy import select

from models import Contact


class ContactsRepository:
    def get_all(self, session: Session) -> List[Contact]:
        """
        Retrieve all contacts from the Contacts table.

        Example usage:
            from db import get_session
            from repositories.contacts_repository import ContactsRepository

            session = get_session()
            try:
                contacts = ContactsRepository().get_all(session)
            finally:
                session.close()
        """
        return session.execute(select(Contact)).scalars().all()
