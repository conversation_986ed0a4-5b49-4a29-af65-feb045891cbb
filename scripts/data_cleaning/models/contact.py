from sqlalchemy import Column, Integer, String, DateTime, Boolean
from db import Base


class Contact(Base):
    __tablename__ = "Contacts"

    idContacts = Column("idContacts", Integer, primary_key=True, autoincrement=True)

    nom = Column(String(255), nullable=True)
    prenom = Column(String(255), nullable=True)
    civilite = Column(String(50), nullable=True)

    email = Column(String(255), nullable=True)
    telephone = Column(String(50), nullable=True)
    telephone2 = Column(String(50), nullable=True)
    telephone3 = Column(String(50), nullable=True)
    mobile = Column(String(50), nullable=True)

    fonction = Column(String(255), nullable=True)
    ct_num = Column(String(255), nullable=True)
    ct_no = Column(String(255), nullable=True)

    dateCreation = Column(DateTime, nullable=True)
    dateModification = Column(DateTime, nullable=True)

    note = Column(String, nullable=True)

    systematique = Column(Boolean, nullable=True)
    facturation = Column(Boolean, nullable=True)
    relance = Column(Boolean, nullable=True)
