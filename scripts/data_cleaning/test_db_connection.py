#!/usr/bin/env python3
"""
Test script to verify database connection functionality.
This script tests both connection string and individual component methods.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from db import get_engine, get_session


def test_connection_with_env_vars():
    """Test database connection using environment variables."""
    print("Testing database connection with environment variables...")
    
    try:
        # Test engine creation
        engine = get_engine()
        print(f"✓ Engine created successfully")
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as test_value")
            row = result.fetchone()
            if row and row[0] == 1:
                print("✓ Database connection successful")
                return True
            else:
                print("✗ Database connection failed - unexpected result")
                return False
                
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False


def test_connection_with_string():
    """Test database connection using a connection string."""
    print("\nTesting database connection with connection string...")
    
    # Get connection string from environment
    conn_str = os.getenv("SQLSERVER_CONNECTION_STRING")
    
    if not conn_str:
        print("⚠ No SQLSERVER_CONNECTION_STRING found in environment, skipping this test")
        return True
    
    try:
        # Test engine creation with connection string
        engine = get_engine(conn_str)
        print(f"✓ Engine created successfully with connection string")
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as test_value")
            row = result.fetchone()
            if row and row[0] == 1:
                print("✓ Database connection successful with connection string")
                return True
            else:
                print("✗ Database connection failed - unexpected result")
                return False
                
    except Exception as e:
        print(f"✗ Database connection failed with connection string: {e}")
        return False


def test_session_creation():
    """Test SQLAlchemy session creation."""
    print("\nTesting session creation...")
    
    try:
        session = get_session()
        print("✓ Session created successfully")
        
        # Test a simple query
        result = session.execute("SELECT 1 as test_value")
        row = result.fetchone()
        if row and row[0] == 1:
            print("✓ Session query successful")
            session.close()
            return True
        else:
            print("✗ Session query failed - unexpected result")
            session.close()
            return False
            
    except Exception as e:
        print(f"✗ Session creation/query failed: {e}")
        return False


def print_connection_info():
    """Print current connection configuration."""
    print("Current connection configuration:")
    print(f"  SQLSERVER_CONNECTION_STRING: {'SET' if os.getenv('SQLSERVER_CONNECTION_STRING') else 'NOT SET'}")
    print(f"  SERVER: {os.getenv('SERVER', 'NOT SET')}")
    print(f"  DATABASE: {os.getenv('DATABASE', 'NOT SET')}")
    print(f"  PORT: {os.getenv('PORT', 'NOT SET')}")
    print(f"  USERNAME: {os.getenv('USERNAME', 'NOT SET')}")
    print(f"  PASSWORD: {'SET' if os.getenv('PASSWORD') else 'NOT SET'}")
    print(f"  DRIVER: {os.getenv('DRIVER', 'NOT SET')}")
    print()


if __name__ == "__main__":
    print("Database Connection Test")
    print("=" * 50)
    
    print_connection_info()
    
    # Run tests
    tests = [
        test_connection_with_env_vars,
        test_connection_with_string,
        test_session_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Database connection is working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check your database configuration.")
        sys.exit(1)
