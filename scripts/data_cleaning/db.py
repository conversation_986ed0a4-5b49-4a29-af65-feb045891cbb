import os
from typing import Optional

from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker, Session

# Base for SQLAlchemy models
Base = declarative_base()

# Environment variable holding the full SQLAlchemy connection string for SQL Server.
# Example:
#   mssql+pyodbc://USER:PASSWORD@HOST:1433/DB_NAME?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes
SQLSERVER_CONNECTION_STRING_ENV = "SQLSERVER_CONNECTION_STRING"


def get_engine(connection_string: Optional[str] = None):
    """
    Create a SQLAlchemy engine for SQL Server using the provided connection string
    or the SQLSERVER_CONNECTION_STRING environment variable.
    """
    url = connection_string or os.getenv(SQLSERVER_CONNECTION_STRING_ENV)

    if not url:
        server = os.getenv("SERVER", "localhost")
        database = os.getenv("DATABASE", "master")
        port = os.getenv("PORT", "1433")
        username = os.getenv("USER", "sa")
        password = os.getenv("PASSWORD", "")
        url = f"mssql+pyodbc://{username}:{password}@{server}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes"

    if not url:
        raise ValueError(
            f"Missing connection string. Provide it as argument or set {SQLSERVER_CONNECTION_STRING_ENV} env var.\n"
            "Example value:\n"
            "mssql+pyodbc://USER:PASSWORD@HOST:1433/DB_NAME"
            "?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes"
        )
    # pool_pre_ping helps keep long-lived connections usable
    return create_engine(url, pool_pre_ping=True, future=True)


def get_session(connection_string: Optional[str] = None) -> Session:
    """
    Create and return a new SQLAlchemy Session.
    The caller is responsible for closing it (e.g., with try/finally or context manager).
    """
    engine = get_engine(connection_string)
    SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)
    return SessionLocal()